<?php if($companies->hasPages()): ?>
<div class="d-flex justify-content-between align-items-center mt-4 flex-wrap gap-3">
    <div class="pagination-info">
        <span class="text-muted small">
            <?php echo e(__('Showing')); ?> 
            <strong><?php echo e($companies->firstItem()); ?></strong> 
            <?php echo e(__('to')); ?> 
            <strong><?php echo e($companies->lastItem()); ?></strong> 
            <?php echo e(__('of')); ?> 
            <strong><?php echo e($companies->total()); ?></strong> 
            <?php echo e(__('results')); ?>

        </span>
    </div>
    <div class="pagination-wrapper">
        <nav aria-label="<?php echo e(__('Pagination Navigation')); ?>" role="navigation">
            <ul class="pagination pagination-sm justify-content-center mb-0">
                
                <?php if($companies->onFirstPage()): ?>
                    <li class="page-item disabled" aria-disabled="true" aria-label="<?php echo app('translator')->get('pagination.previous'); ?>">
                        <span class="page-link" aria-hidden="true">
                            <i class="ti ti-chevron-left"></i>
                        </span>
                    </li>
                <?php else: ?>
                    <li class="page-item">
                        <a class="page-link pagination-link" href="#" data-page="<?php echo e($companies->currentPage() - 1); ?>" rel="prev" aria-label="<?php echo app('translator')->get('pagination.previous'); ?>">
                            <i class="ti ti-chevron-left"></i>
                        </a>
                    </li>
                <?php endif; ?>

                
                <?php
                    $start = max($companies->currentPage() - 2, 1);
                    $end = min($start + 4, $companies->lastPage());
                    $start = max($end - 4, 1);
                ?>

                <?php if($start > 1): ?>
                    <li class="page-item">
                        <a class="page-link pagination-link" href="#" data-page="1">1</a>
                    </li>
                    <?php if($start > 2): ?>
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>

                <?php for($page = $start; $page <= $end; $page++): ?>
                    <?php if($page == $companies->currentPage()): ?>
                        <li class="page-item active" aria-current="page">
                            <span class="page-link"><?php echo e($page); ?></span>
                        </li>
                    <?php else: ?>
                        <li class="page-item">
                            <a class="page-link pagination-link" href="#" data-page="<?php echo e($page); ?>"><?php echo e($page); ?></a>
                        </li>
                    <?php endif; ?>
                <?php endfor; ?>

                <?php if($end < $companies->lastPage()): ?>
                    <?php if($end < $companies->lastPage() - 1): ?>
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    <?php endif; ?>
                    <li class="page-item">
                        <a class="page-link pagination-link" href="#" data-page="<?php echo e($companies->lastPage()); ?>"><?php echo e($companies->lastPage()); ?></a>
                    </li>
                <?php endif; ?>

                
                <?php if($companies->hasMorePages()): ?>
                    <li class="page-item">
                        <a class="page-link pagination-link" href="#" data-page="<?php echo e($companies->currentPage() + 1); ?>" rel="next" aria-label="<?php echo app('translator')->get('pagination.next'); ?>">
                            <i class="ti ti-chevron-right"></i>
                        </a>
                    </li>
                <?php else: ?>
                    <li class="page-item disabled" aria-disabled="true" aria-label="<?php echo app('translator')->get('pagination.next'); ?>">
                        <span class="page-link" aria-hidden="true">
                            <i class="ti ti-chevron-right"></i>
                        </span>
                    </li>
                <?php endif; ?>
            </ul>
        </nav>
    </div>
</div>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\new_laravel_project\omx-new-saas\resources\views/system_admin/companies/partials/pagination.blade.php ENDPATH**/ ?>