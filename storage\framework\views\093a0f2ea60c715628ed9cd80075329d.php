<table class="table table-striped align-middle mb-0">
    <thead class="table-light">
        <tr>
            <th><?php echo e(__('Company Name')); ?></th>
            <th><?php echo e(__('Email')); ?></th>
            <th><?php echo e(__('Plan')); ?></th>
            <th><?php echo e(__('Last Login')); ?></th>
            <th><?php echo e(__('Users')); ?></th>
            <th><?php echo e(__('Customers')); ?></th>
            <th><?php echo e(__('Vendors')); ?></th>
            <th><?php echo e(__('Status')); ?></th>
            <th><?php echo e(__('Actions')); ?></th>
        </tr>
    </thead>
    <tbody>
        <?php $__currentLoopData = $companies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $company): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <tr>
                <td class="fw-semibold">
                    <div class="d-flex align-items-center gap-2">
                        <img src="<?php echo e(!empty($company->avatar) ? Utility::get_file('uploads/avatar/') . $company->avatar : asset(Storage::url('uploads/avatar/avatar.png'))); ?>" alt="user-image" class="rounded-circle border border-primary" style="width:36px;height:36px;object-fit:cover;">
                        <span><?php echo e($company->name); ?></span>
                    </div>
                </td>
                <td class="text-break"><?php echo e($company->email); ?></td>
                <td>
                    <span class="badge bg-primary">
                        <?php echo e(!empty($company->plan) && is_object($company->plan) ? $company->plan->name : (!empty($company->plan) ? 'Plan ID: ' . $company->plan : __('No Plan'))); ?>

                    </span>
                </td>
                <td>
                    <?php if($company->last_login_at): ?>
                        <?php echo e(\Carbon\Carbon::parse($company->last_login_at)->format('M d, Y \a\t H:i')); ?>

                    <?php else: ?>
                        <span class="text-muted"><?php echo e(__('Never logged in')); ?></span>
                    <?php endif; ?>
                </td>
                <td><?php echo e($company->totalCompanyUser($company->id)); ?></td>
                <td><?php echo e($company->totalCompanyCustomer($company->id)); ?></td>
                <td><?php echo e($company->totalCompanyVender($company->id)); ?></td>
                <td>
                    <?php if($company->delete_status == 0): ?>
                        <span class="badge bg-danger"><?php echo e(__('Soft Deleted')); ?></span>
                    <?php else: ?>
                        <span class="badge bg-success"><?php echo e(__('Active')); ?></span>
                    <?php endif; ?>
                </td>
                <td>
                    <div class="btn-group">
                        <?php if(\Auth::user()->type == 'system admin' || (\Auth::user()->type == 'staff' && \Auth::user()->can('manage sub accounts'))): ?>
                        <a href="<?php echo e(route('login.with.company', $company->id)); ?>"
                            data-bs-toggle="tooltip"
                            title="<?php echo e(__('Login')); ?>"
                            style="display:inline-flex;align-items:center;gap:8px;padding:0 16px;height:40px;border-radius:999px;background:linear-gradient(to right, #065f46, #0f766e);color:white;font-weight:500;box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;"
                            onmouseover="this.style.transform='scale(1.05)'"
                            onmouseout="this.style.transform='scale(1)'">
                            <i class="ti ti-replace" style="font-size:16px;"></i>
                            <span><?php echo e(__('Login')); ?></span>
                        </a>
                        <?php endif; ?>
                        <?php if(\Auth::user()->type == 'system admin' || (\Auth::user()->type == 'staff' && \Auth::user()->can('edit sub accounts'))): ?>
                        <a href="<?php echo e(route('system-admin.companies.edit', $company->id)); ?>"
                            data-bs-toggle="tooltip"
                            title="<?php echo e(__('Edit')); ?>"
                            style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;border-radius:50%;
                                    background:linear-gradient(to right, #065f46, #0f766e);color:white;
                                    box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out; margin-left:5px;"
                            onmouseover="this.style.transform='scale(1.1)'"
                            onmouseout="this.style.transform='scale(1)'">
                            <i class="ti ti-pencil"></i>
                        </a>
                        <?php endif; ?>
                        <?php if(\Auth::user()->type == 'system admin' || (\Auth::user()->type == 'staff' && \Auth::user()->can('delete sub accounts'))): ?>
                            <?php echo Form::open(['method' => 'DELETE', 'route' => ['system-admin.companies.destroy', $company['id']], 'id' => 'delete-form-' . $company['id'], 'style' => 'display:inline-block']); ?>

                            <button type="button"
                                data-bs-toggle="tooltip"
                                title="<?php echo e(__('Delete Company')); ?>"
                                class="bs-pass-para-delete"
                                data-confirm-text="<?php echo e(__('Are you sure you want to delete this company?')); ?>"
                                style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                                        border-radius:50%;background:white;color:#dc2626;border:1.5px solid #dc2626;
                                        box-shadow:0 4px 6px rgba(0,0,0,0.05);transition:transform 0.2s ease-in-out; margin-left:5px;"
                                onmouseover="this.style.transform='scale(1.1)'"
                                onmouseout="this.style.transform='scale(1)'">
                                <i class="ti ti-trash"></i>
                            </button>
                            <?php echo Form::close(); ?>

                            <?php if($company->delete_status != 0): ?>
                                <?php echo Form::open(['method' => 'DELETE', 'route' => ['system-admin.companies.force-destroy', $company['id']], 'id' => 'force-delete-form-' . $company['id'], 'style' => 'display:inline-block']); ?>

                                <button type="button"
                                    class="bs-pass-para-force"
                                    data-confirm-text="This will force delete the company even if modules have dependency errors. Are you sure?"
                                    title="<?php echo e(__('Force Delete')); ?>"
                                    data-bs-toggle="tooltip"
                                    style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                                            border-radius:50%;background:white;color:#dc2626;border:1.5px solid #dc2626;
                                            box-shadow:0 4px 6px rgba(0,0,0,0.05);transition:transform 0.2s ease-in-out; margin-left:5px;"
                                    onmouseover="this.style.transform='scale(1.1)'"
                                    onmouseout="this.style.transform='scale(1)'">
                                    <i class="ti ti-alert-triangle"></i>
                                </button>
                                <?php echo Form::close(); ?>

                            <?php endif; ?>
                        <?php endif; ?>
                        <?php if($company->is_enable_login == 1): ?>
                        <a href="<?php echo e(route('system-admin.companies.login', \Crypt::encrypt($company->id))); ?>"
                            title="<?php echo e(__('Login Disable')); ?>"
                            data-bs-toggle="tooltip"
                            style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                                    border-radius:50%;background:white;color:#eab308;border:1.5px solid #eab308;
                                    box-shadow:0 4px 6px rgba(0,0,0,0.05);transition:transform 0.2s ease-in-out; margin-left:5px;"
                            onmouseover="this.style.transform='scale(1.1)'"
                            onmouseout="this.style.transform='scale(1)'">
                            <i class="ti ti-road-sign"></i>
                        </a>
                        <?php elseif($company->is_enable_login == 0 && $company->password == null): ?>
                        <a href="#"
                            data-url="<?php echo e(route('system-admin.companies.reset', \Crypt::encrypt($company->id))); ?>"
                            data-ajax-popup="true"
                            data-size="md"
                            data-bs-toggle="tooltip"
                            title="<?php echo e(__('New Password')); ?>"
                            class="login_enable"
                            style="display:inline-flex;align-items:center;gap:8px;padding:0 16px;height:40px;border-radius:999px;
                                    background:linear-gradient(to right, #065f46, #0f766e);color:white;font-weight:500;
                                    box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;"
                            onmouseover="this.style.transform='scale(1.05)'"
                            onmouseout="this.style.transform='scale(1)'">
                            <i class="ti ti-road-sign" style="font-size:16px;"></i>
                        </a>
                        <?php else: ?>
                        <a href="<?php echo e(route('system-admin.companies.login', \Crypt::encrypt($company->id))); ?>"
                            data-bs-toggle="tooltip"
                            title="<?php echo e(__('Login Enable')); ?>"
                            style="display:inline-flex;align-items:center;gap:8px;padding:0 16px;height:40px;border-radius:999px;
                                    background:linear-gradient(to right, #065f46, #0f766e);color:white;font-weight:500;
                                    box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out; margin-left:5px;"
                            onmouseover="this.style.transform='scale(1.05)'"
                            onmouseout="this.style.transform='scale(1)'">
                            <i class="ti ti-road-sign" style="font-size:16px;"></i>
                        </a>
                        <?php endif; ?>
                    </div>
                </td>
            </tr>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </tbody>
</table>
<?php /**PATH C:\Users\<USER>\Desktop\new_laravel_project\omx-new-saas\resources\views/system_admin/companies/partials/table.blade.php ENDPATH**/ ?>